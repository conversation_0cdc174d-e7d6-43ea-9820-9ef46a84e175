/* ===================================
   RESPONSIVE DESIGN - TC WEHEN
   Mobile-First Responsive Breakpoints
   =================================== */

/* ===== MOBILE FIRST APPROACH ===== */
/* Base styles are mobile (320px+) */

/* ===== SMALL MOBILE (320px - 479px) ===== */
@media (max-width: 479px) {
    .container {
        padding: 0 var(--space-sm);
    }
    
    .hero-title {
        font-size: 2rem;
        line-height: 1.1;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .hero-description {
        font-size: 1rem;
        padding: 0 var(--space-xs);
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: var(--space-sm);
    }
    
    .stat-number {
        font-size: 1.75rem;
    }
    
    .stat-label {
        font-size: 0.875rem;
    }
    
    .btn {
        padding: var(--space-sm) var(--space-md);
        font-size: 0.875rem;
    }
    
    .nav-title {
        font-size: 1.25rem;
    }
    
    .nav-logo {
        height: 35px;
    }
}

/* ===== MOBILE LANDSCAPE (480px - 767px) ===== */
@media (min-width: 480px) and (max-width: 767px) {
    .hero-buttons {
        flex-direction: row;
        justify-content: center;
    }
    
    .hero-buttons .btn {
        width: auto;
        min-width: 160px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .facilities-grid {
        grid-template-columns: 1fr;
    }
    
    .news-grid {
        grid-template-columns: 1fr;
    }
}

/* ===== TABLET PORTRAIT (768px - 991px) ===== */
@media (min-width: 768px) and (max-width: 991px) {
    .hero {
        height: 90vh;
    }
    
    .hero-title {
        font-size: 3rem;
    }
    
    .hero-description {
        font-size: 1.125rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--space-lg);
    }
    
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
    }
    
    .facilities-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .news-grid {
        grid-template-columns: 1fr 1fr;
        gap: var(--space-lg);
    }
    
    .news-card.featured {
        grid-column: span 2;
        grid-row: span 1;
    }
    
    .membership-content {
        grid-template-columns: 1fr;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
    }
    
    .trainer-info {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .tennis-school-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

/* ===== TABLET LANDSCAPE / SMALL DESKTOP (992px - 1199px) ===== */
@media (min-width: 992px) and (max-width: 1199px) {
    /* Desktop Navigation - Clean & Simple */
    .nav-menu {
        gap: var(--space-lg);
    }

    .nav-link {
        padding: var(--space-sm) var(--space-md);
        font-size: 0.95rem;
        font-weight: 400;
    }

    .nav-link.active {
        color: var(--primary-color);
        font-weight: 500;
    }

    .hero-title {
        font-size: 3.25rem;
    }

    .facilities-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .news-grid {
        grid-template-columns: 2fr 1fr 1fr;
    }

    .about-content {
        grid-template-columns: 1fr 1fr;
    }

    .membership-content {
        grid-template-columns: 2fr 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr 1fr;
    }

    .trainer-info {
        grid-template-columns: auto 1fr;
    }

    .tennis-school-content {
        grid-template-columns: 1fr auto;
    }
}

/* ===== LARGE DESKTOP (1200px+) ===== */
@media (min-width: 1200px) {
    /* Clean Desktop Navigation */
    .nav-menu {
        gap: var(--space-xl);
    }

    .nav-link {
        padding: var(--space-sm) var(--space-lg);
        font-size: 1rem;
        font-weight: 400;
    }

    .nav-link.active {
        color: var(--primary-color);
        font-weight: 500;
    }

    .hero-title {
        font-size: 3.5rem;
    }

    .hero-description {
        font-size: 1.25rem;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .facilities-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .news-grid {
        grid-template-columns: 2fr 1fr 1fr;
    }

    /* Enhanced hover effects for desktop */
    .facility-card:hover {
        transform: translateY(-8px);
    }

    .news-card:hover {
        transform: translateY(-5px);
    }

    .contact-card:hover {
        transform: translateY(-5px);
    }
}

/* ===== EXTRA LARGE DESKTOP (1400px+) ===== */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }

    /* Extra Large Desktop Navigation */
    .nav-menu {
        gap: var(--space-xl);
    }

    .nav-link {
        padding: var(--space-md) var(--space-xl);
        font-size: 1.05rem;
        font-weight: 400;
    }

    .nav-link.active {
        color: var(--primary-color);
        font-weight: 500;
    }

    .hero-title {
        font-size: 4rem;
    }

    .section-title {
        font-size: 3rem;
    }
}

/* ===== LANDSCAPE ORIENTATION ADJUSTMENTS ===== */
@media (orientation: landscape) and (max-height: 600px) {
    .hero {
        height: 100vh;
        min-height: 500px;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-description {
        font-size: 1rem;
    }
    
    .hero-buttons {
        margin-top: var(--space-md);
    }
}

/* ===== HIGH DPI / RETINA DISPLAYS ===== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Ensure crisp images on retina displays */
    .nav-logo,
    .footer-logo img,
    .partner-logo,
    .htv-logo,
    .school-logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .navbar,
    .hero-dots,
    .btn,
    .contact-form,
    .footer {
        display: none !important;
    }
    
    .hero {
        height: auto;
        min-height: auto;
        page-break-inside: avoid;
    }
    
    .hero-content {
        color: var(--text-dark) !important;
    }
    
    section {
        page-break-inside: avoid;
        padding: var(--space-lg) 0;
    }
    
    .container {
        max-width: none;
        padding: 0;
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .hero-slide {
        transition: none;
    }
    
    html {
        scroll-behavior: auto;
    }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
    /* Optional: Add dark mode styles if needed */
    /* Currently maintaining light theme for tennis club aesthetic */
}

/* ===== TOUCH DEVICE OPTIMIZATIONS ===== */
@media (hover: none) and (pointer: coarse) {
    /* Remove hover effects on touch devices */
    .facility-card:hover,
    .news-card:hover,
    .contact-card:hover {
        transform: none;
    }
    
    /* Increase touch targets */
    .nav-link {
        padding: var(--space-md);
    }
    
    .btn {
        min-height: 48px;
        padding: var(--space-md) var(--space-lg);
    }
    
    .hero-dot {
        width: 16px;
        height: 16px;
    }
}

/* ===== FOCUS IMPROVEMENTS FOR KEYBOARD NAVIGATION ===== */
@media (hover: hover) and (pointer: fine) {
    /* Enhanced focus styles for mouse users */
    .nav-link:focus-visible,
    .btn:focus-visible {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }
}

/* ===== CONTAINER QUERIES (Future-proofing) ===== */
/* When container queries are widely supported, these can be uncommented */
/*
@container (max-width: 400px) {
    .facility-card {
        padding: var(--space-md);
    }
}
*/
