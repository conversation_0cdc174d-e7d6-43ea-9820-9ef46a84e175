# TC Wehen Website 🎾

Eine moderne, mobile-first Website für den Tennis-Club Wehen e.V. in Taunusstein.

## 🌟 Features

- **Mobile-First Design** - Optimiert für Smartphones und Tablets
- **Premium Look** - Inspiriert von hochwertigen Tennis- und Golfclub-Websites
- **Deutsche Sprache** - Vollständig auf Deutsch für Vereinsmitglieder
- **Einfache News-Verwaltung** - Neue Artikel durch HTML-Bearbeitung hinzufügen
- **Responsive Design** - Perfekte Darstellung auf allen Geräten
- **Schnelle Ladezeiten** - Optimiert für mobile Datenverbindungen
- **Barrierefreie Navigation** - Tastatur- und Touch-freundlich

## 📁 Dateistruktur

```
TC-Wehen-Website/
├── index.html              # Startseite
├── news.html               # Neuigkeiten-Seite
├── ueber-uns.html          # Über uns Seite
├── clubanlage.html         # Clubanlage Seite
├── tennisschule.html       # Tennisschule Seite
├── mitgliedschaft.html     # Mitgliedschaft Seite
├── kontakt.html            # Kontakt Seite
├── impressum.html          # Impressum
├── datenschutz.html        # Datenschutzerklärung
├── css/
│   ├── main.css            # Grundlegende Styles und Design-System
│   ├── components.css      # Komponenten-spezifische Styles
│   ├── responsive.css      # Responsive Breakpoints
│   └── pages.css           # Styles für Unterseiten
├── js/
│   ├── main.js             # Hauptfunktionalität
│   ├── hero-slider.js      # Bildslider für Hero-Bereich
│   ├── forms.js            # Kontaktformular-Funktionen
│   └── news.js             # News-Seite Funktionalität
├── assets/                 # Bilder und Logos
└── README.md              # Diese Datei
```

## 🔧 News hinzufügen (Einfach!)

### Schritt-für-Schritt Anleitung:

1. **Öffnen Sie `news.html`** in einem Texteditor
2. **Finden Sie den Kommentar** mit der Anleitung (Zeile ~70)
3. **Kopieren Sie diesen Block:**

```html
<article class="news-item">
    <div class="news-item-image">
        <img src="assets/BILD.PNG" alt="Beschreibung des Bildes">
        <div class="news-category">KATEGORIE</div>
    </div>
    <div class="news-item-content">
        <span class="news-date">DD. MONAT YYYY</span>
        <h2>TITEL DER NEWS</h2>
        <p class="news-excerpt">Kurze Zusammenfassung...</p>
        <div class="news-full-content">
            <p>Vollständiger Text hier...</p>
        </div>
        <button class="read-more-btn">Weiterlesen</button>
    </div>
</article>
```

4. **Ersetzen Sie:**
   - `BILD.PNG` → Ihr Bildname (z.B. `neues-turnier.jpg`)
   - `KATEGORIE` → Eine von: `Vereinsnews`, `Training`, `Turniere`, `Events`, `Mitglieder`
   - `DD. MONAT YYYY` → Datum (z.B. `25. Februar 2024`)
   - `TITEL DER NEWS` → Ihr Titel
   - `Kurze Zusammenfassung...` → Kurze Beschreibung
   - `Vollständiger Text hier...` → Kompletter Artikel

5. **Fügen Sie den Block OBEN** in die News-Liste ein (neueste zuerst)
6. **Speichern Sie die Datei**

### Kategorien:
- 🏆 **Turniere** - Turnierergebnisse, Anmeldungen
- 📢 **Vereinsnews** - Allgemeine Vereinsinformationen
- 🎾 **Training** - Trainingskurse, Tennisschule
- 🎉 **Events** - Veranstaltungen, Feiern
- 📋 **Mitglieder** - Neue Mitglieder, Ehrungen

## 🎨 Design-System

### Farben:
- **Primärfarbe:** `#2d5016` (Tennis-Grün)
- **Sekundärfarbe:** `#c8a882` (Sandplatz-Beige)
- **Akzentfarbe:** `#f4f1eb` (Cremeweiß)

### Schriftarten:
- **Überschriften:** Playfair Display (elegant)
- **Fließtext:** Inter (modern, lesbar)

### Breakpoints:
- **Mobile:** 320px - 767px
- **Tablet:** 768px - 991px
- **Desktop:** 992px+

## 📱 Mobile Optimierungen

- **Touch-freundliche Buttons** (mindestens 44px)
- **Lesbare Schriftgrößen** auf kleinen Bildschirmen
- **Optimierte Bilder** für schnelle Ladezeiten
- **Hamburger-Menü** für mobile Navigation
- **Swipe-Gesten** für Bildslider

## 🚀 Performance

- **Lazy Loading** für Bilder
- **Optimierte CSS** mit CSS Custom Properties
- **Minimale JavaScript** für schnelle Ladezeiten
- **Progressive Enhancement** - funktioniert auch ohne JavaScript

## 🔧 Wartung

### Bilder hinzufügen:
1. Bilder in den `assets/` Ordner kopieren
2. Empfohlene Größen:
   - **Hero-Bilder:** 1920x1080px
   - **News-Bilder:** 800x400px
   - **Logos:** PNG mit transparentem Hintergrund

### Kontaktdaten ändern:
- In `index.html` im Kontakt-Bereich
- Im Footer beider Seiten

### Farben anpassen:
- CSS Custom Properties in `css/main.css` (Zeile 8-20)

## 🌐 Browser-Unterstützung

- ✅ Chrome (Android/Desktop)
- ✅ Safari (iOS/macOS)
- ✅ Firefox
- ✅ Edge
- ✅ Samsung Internet

## 📞 Support

Bei Fragen zur Website-Wartung:
- Schauen Sie in die HTML-Kommentare
- Alle Änderungen sind in den Dateien dokumentiert
- Die Struktur ist bewusst einfach gehalten

## 🎾 Über TC Wehen

**Tennis-Club Wehen e.V.**
- Gegründet 1972 in Taunusstein-Wehen
- Platter Straße 89, 65232 Taunusstein
- 6 gepflegte Sandplätze mit Flutlichtanlage
- Über 120 Mitglieder in familiärer Atmosphäre
- Mitglied im Hessischen Tennis-Verband (HTV)
- Tennisschule Prätorius mit lizenziertem Trainer Frank
- Aktive Mannschaften in allen Altersklassen

---

*Erstellt mit ❤️ für den TC Wehen - Ihr Tennisverein in Taunusstein*
