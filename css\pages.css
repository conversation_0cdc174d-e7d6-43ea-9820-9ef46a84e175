/* ===================================
   PAGE STYLES - TC WEHEN
   Consistent styling for all pages
   =================================== */

/* ===== PAGE HEADER ===== */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    padding: calc(80px + var(--space-xl)) 0 var(--space-xxl) 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../assets/Ball.PNG') center/cover;
    opacity: 0.1;
    z-index: 0;
}

.page-header .container {
    position: relative;
    z-index: 1;
}

.page-title {
    font-family: var(--font-primary);
    font-size: 3rem;
    font-weight: 600;
    margin-bottom: var(--space-sm);
    color: var(--white);
}

.page-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
    color: var(--white);
}

/* Mobile Page Header */
@media (max-width: 767px) {
    .page-header {
        padding: calc(70px + var(--space-lg)) 0 var(--space-xl) 0;
    }
    
    .page-title {
        font-size: 2.25rem;
    }
    
    .page-subtitle {
        font-size: 1.125rem;
        padding: 0 var(--space-sm);
    }
}

/* ===== MAIN CONTENT ===== */
.main-content {
    background-color: var(--white);
    padding: var(--space-xxl) 0;
    min-height: 60vh;
}

.content-section {
    max-width: 800px;
    margin: 0 auto;
}

.content-section h2 {
    color: var(--primary-color);
    margin-bottom: var(--space-lg);
    text-align: center;
    position: relative;
}

.content-section h2::after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
    margin: var(--space-sm) auto;
    border-radius: var(--radius-sm);
}

.content-section h3 {
    color: var(--primary-color);
    margin-top: var(--space-xl);
    margin-bottom: var(--space-md);
}

.content-section p {
    margin-bottom: var(--space-md);
    line-height: 1.7;
}

.content-section ul {
    margin-bottom: var(--space-lg);
    padding-left: var(--space-lg);
}

.content-section li {
    margin-bottom: var(--space-sm);
    color: var(--text-light);
    line-height: 1.6;
}

/* ===== BOARD MEMBERS GRID ===== */
.board-members {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-lg);
    margin: var(--space-xl) 0;
}

.board-member {
    background-color: var(--accent-color);
    padding: var(--space-lg);
    border-radius: var(--radius-lg);
    text-align: center;
    transition: var(--transition-medium);
    border: 1px solid var(--border-light);
}

.board-member:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px var(--shadow-light);
}

.board-member h4 {
    color: var(--primary-color);
    margin-bottom: var(--space-sm);
    font-size: 1.125rem;
}

.board-member p {
    margin-bottom: 0;
    color: var(--text-light);
}

.board-member a {
    color: var(--primary-color);
    font-weight: 500;
}

/* Mobile Board Members */
@media (max-width: 767px) {
    .board-members {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }
}

/* ===== CONTACT GRID ===== */
.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-lg);
    margin: var(--space-xl) 0;
}

.contact-card {
    background-color: var(--accent-color);
    padding: var(--space-lg);
    border-radius: var(--radius-lg);
    text-align: center;
    transition: var(--transition-medium);
    border: 1px solid var(--border-light);
}

.contact-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px var(--shadow-light);
}

.contact-card h3 {
    color: var(--primary-color);
    margin-bottom: var(--space-md);
    font-size: 1.25rem;
}

.contact-card p {
    margin-bottom: 0;
    color: var(--text-light);
    line-height: 1.6;
}

/* ===== CONTACT PERSONS ===== */
.board-contacts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-lg);
    margin: var(--space-xl) 0;
}

.contact-person {
    background-color: var(--background);
    padding: var(--space-lg);
    border-radius: var(--radius-lg);
    text-align: center;
    transition: var(--transition-medium);
    border: 1px solid var(--border-light);
}

.contact-person:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px var(--shadow-light);
}

.contact-person h4 {
    color: var(--primary-color);
    margin-bottom: var(--space-sm);
    font-size: 1.125rem;
}

.contact-person p {
    margin-bottom: 0;
    color: var(--text-light);
}

.contact-person a {
    color: var(--primary-color);
    font-weight: 500;
}

/* ===== CONTACT INFO SECTION ===== */
.contact-info {
    background-color: var(--accent-color);
    padding: var(--space-xl);
    border-radius: var(--radius-lg);
    margin: var(--space-xl) 0;
    border: 1px solid var(--border-light);
}

.contact-info h4 {
    color: var(--primary-color);
    margin-bottom: var(--space-md);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: var(--space-sm);
}

.contact-info p {
    margin-bottom: var(--space-sm);
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 767px) {
    .main-content {
        padding: var(--space-xl) 0;
    }
    
    .content-section {
        padding: 0 var(--space-sm);
    }
    
    .contact-grid {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }
    
    .board-contacts {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }
    
    .contact-info {
        padding: var(--space-lg);
        margin: var(--space-lg) 0;
    }
}

/* ===== SPECIAL STYLING FOR LINKS ===== */
.content-section a.btn {
    display: inline-block;
    margin: var(--space-md) 0;
}

.content-section a[href^="http"]:not(.btn)::after {
    content: " ↗";
    font-size: 0.875rem;
    opacity: 0.7;
}

/* ===== EMPHASIS STYLING ===== */
.content-section em {
    color: var(--text-muted);
    font-style: italic;
    background-color: var(--background);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--primary-color);
    display: block;
    margin: var(--space-md) 0;
}
